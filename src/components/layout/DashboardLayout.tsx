import React from 'react';
import { theme } from '../../config/theme.config';

interface DashboardLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  actions?: React.ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  title,
  description,
  actions
}) => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: theme.spacing.lg,
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.md,
      }}>
        <div>
          <h2 style={{
            margin: 0,
            color: theme.colors.text.primary,
            fontSize: '28px',
            fontWeight: 600,
          }}>
            {title}
          </h2>
          {description && (
            <p style={{
              margin: `${theme.spacing.xs}px 0 0 0`,
              color: theme.colors.text.secondary,
              fontSize: '14px',
            }}>
              {description}
            </p>
          )}
        </div>
        {actions && (
          <div style={{
            display: 'flex',
            gap: theme.spacing.sm,
          }}>
            {actions}
          </div>
        )}
      </div>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(12, 1fr)',
        gap: theme.spacing.lg,
        gridAutoRows: 'minmax(120px, auto)',
        padding: `0 ${theme.spacing.md}px`,
      }}>
        {children}
      </div>
    </div>
  );
};