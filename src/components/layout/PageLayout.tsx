import React from 'react';
import { theme } from '../../config/theme.config';

interface PageLayoutProps {
  children: React.ReactNode;
  title?: string;
  showHeader?: boolean;
}

export const PageLayout: React.FC<PageLayoutProps> = ({ 
  children, 
  title,
  showHeader = true 
}) => {
  return (
    <div style={{ 
      minHeight: '100vh',
      backgroundColor: theme.colors.background,
      display: 'flex',
      flexDirection: 'column'
    }}>
      {showHeader && (
        <header style={{
          backgroundColor: theme.colors.surface,
          boxShadow: theme.shadows.sm,
          padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
          position: 'sticky',
          top: 0,
          zIndex: 1000,
        }}>
          <h1 style={{
            margin: 0,
            color: theme.colors.text.primary,
            fontSize: '24px',
            fontWeight: 600,
          }}>
            {title || 'Sisense Dashboard'}
          </h1>
        </header>
      )}
      <main style={{
        flex: 1,
        padding: theme.spacing.lg,
      }}>
        {children}
      </main>
    </div>
  );
};