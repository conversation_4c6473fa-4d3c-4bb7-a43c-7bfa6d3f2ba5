import React from 'react';
import { theme } from '../../config/theme.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface WidgetContainerProps {
  children: React.ReactNode;
  title?: string;
  position: WidgetPosition;
  loading?: boolean;
  error?: Error | null;
}

export const WidgetContainer: React.FC<WidgetContainerProps> = ({
  children,
  title,
  position,
  loading = false,
  error = null,
}) => {
  const gridStyles = {
    gridColumn: `span ${position.w}`,
    gridRow: `span ${position.h}`,
  };

  return (
    <div
      style={{
        ...gridStyles,
        backgroundColor: theme.colors.surface,
        borderRadius: theme.borderRadius.widget,
        boxShadow: theme.shadows.widget,
        padding: theme.spacing.widget,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        transition: `all ${theme.transitions.normal}`,
        border: `1px solid ${theme.colors.border.light}`,
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.cardHover;
        e.currentTarget.style.transform = 'translateY(-2px)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.widget;
        e.currentTarget.style.transform = 'translateY(0)';
      }}
    >
      {title && (
        <h3 style={{
          margin: `0 0 ${theme.spacing.lg}px 0`,
          color: theme.colors.text.primary,
          fontSize: theme.typography.fontSize.lg,
          fontWeight: theme.typography.fontWeight.semibold,
        }}>
          {title}
        </h3>
      )}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: loading || error ? 'center' : 'stretch',
        minHeight: 0,
      }}>
        {loading && (
          <div style={{ color: theme.colors.text.secondary }}>
            Loading...
          </div>
        )}
        {error && (
          <div style={{ color: theme.colors.error, textAlign: 'center' }}>
            Error: {error.message}
          </div>
        )}
        {!loading && !error && children}
      </div>
    </div>
  );
};