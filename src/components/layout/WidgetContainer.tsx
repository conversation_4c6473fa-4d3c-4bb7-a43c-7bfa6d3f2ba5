import React from 'react';
import { theme } from '../../config/theme.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface WidgetContainerProps {
  children: React.ReactNode;
  title?: string;
  position: WidgetPosition;
  loading?: boolean;
  error?: Error | null;
}

export const WidgetContainer: React.FC<WidgetContainerProps> = ({
  children,
  title,
  position,
  loading = false,
  error = null,
}) => {
  const gridStyles = {
    gridColumn: `span ${position.w}`,
    gridRow: `span ${position.h}`,
  };

  return (
    <div
      style={{
        ...gridStyles,
        backgroundColor: theme.colors.surface,
        borderRadius: theme.borderRadius.md,
        boxShadow: theme.shadows.sm,
        padding: theme.spacing.xl,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        transition: `box-shadow ${theme.transitions.fast}`,
        border: `1px solid ${theme.colors.border.light}`,
        minHeight: '180px',
        height: '100%',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.md;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.sm;
      }}
    >
      {title && (
        <h3 style={{
          margin: `0 0 ${theme.spacing.md}px 0`,
          color: theme.colors.text.primary,
          fontSize: theme.typography.fontSize.md,
          fontWeight: theme.typography.fontWeight.medium,
          letterSpacing: '-0.2px',
        }}>
          {title}
        </h3>
      )}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: loading || error ? 'center' : 'stretch',
        minHeight: 0,
        width: '100%',
      }}>
        {loading && (
          <div style={{ 
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}>
            Loading widget...
          </div>
        )}
        {error && (
          <div style={{ 
            color: theme.colors.error, 
            textAlign: 'center',
            fontSize: theme.typography.fontSize.sm,
            padding: theme.spacing.md,
          }}>
            Error: {error.message}
          </div>
        )}
        {!loading && !error && children}
      </div>
    </div>
  );
};