import React from 'react';
import { WidgetContainer } from '../../layout/WidgetContainer';
import { theme } from '../../../config/theme.config';
import type { WidgetPosition } from '../../../types/dashboard.types';

interface PlaceholderWidgetProps {
  title?: string;
  position: WidgetPosition;
  type: 'chart' | 'kpi' | 'table' | 'filter' | 'header' | 'info' | 'text';
  widgetId: string;
  config?: any;
}

export const PlaceholderWidget: React.FC<PlaceholderWidgetProps> = ({
  title,
  position,
  type,
  widgetId,
  config = {},
}) => {
  const getPlaceholderContent = () => {
    switch (type) {
      case 'kpi':
        return (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            padding: theme.spacing.lg,
            background: config.color ? `linear-gradient(135deg, ${config.color}10, ${config.color}05)` : 'transparent',
          }}>
            <div style={{
              fontSize: position.w > 2 ? '48px' : '32px',
              fontWeight: theme.typography.fontWeight.bold,
              color: config.color || theme.colors.primary,
              marginBottom: theme.spacing.sm,
              textAlign: 'center',
            }}>
              {config.value || '$0.00'}
            </div>
            <div style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
              textAlign: 'center',
              marginBottom: theme.spacing.xs,
            }}>
              {title || 'KPI Value'}
            </div>
            {config.subtitle && (
              <div style={{
                fontSize: theme.typography.fontSize.xs,
                color: theme.colors.text.secondary,
                textAlign: 'center',
                opacity: 0.8,
              }}>
                {config.subtitle}
              </div>
            )}
          </div>
        );
      
      case 'chart':
        if (config.chartType === 'pie' && config.data) {
          const total = config.data.reduce((sum: number, item: any) => sum + item.value, 0);
          let currentAngle = 0;

          return (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              height: '100%',
              padding: theme.spacing.lg,
              gap: theme.spacing.lg,
            }}>
              <div style={{
                width: '140px',
                height: '140px',
                borderRadius: '50%',
                background: `conic-gradient(${config.data.map((item: any) => {
                  const percentage = (item.value / total) * 360;
                  const gradient = `${item.color || theme.colors.primary} ${currentAngle}deg ${currentAngle + percentage}deg`;
                  currentAngle += percentage;
                  return gradient;
                }).join(', ')})`,
                position: 'relative',
                boxShadow: theme.shadows.sm,
              }}>
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  background: theme.colors.surface,
                  borderRadius: '50%',
                  width: '70px',
                  height: '70px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: theme.typography.fontSize.base,
                  fontWeight: theme.typography.fontWeight.bold,
                  color: theme.colors.text.primary,
                  boxShadow: theme.shadows.inner,
                }}>
                  {config.data[0]?.value}%
                </div>
              </div>
              <div style={{ flex: 1 }}>
                {config.data.map((item: any, index: number) => (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: theme.spacing.sm,
                    fontSize: theme.typography.fontSize.sm,
                  }}>
                    <div style={{
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      backgroundColor: item.color || theme.colors.primary,
                      marginRight: theme.spacing.sm,
                    }} />
                    <span style={{ color: theme.colors.text.secondary }}>
                      {item.mode} ({item.value}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          );
        }

        if (config.chartType === 'column' && config.data) {
          const maxValue = Math.max(...config.data.map((d: any) => d.value));
          const chartHeight = 120;

          return (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              padding: theme.spacing.lg,
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'end',
                justifyContent: 'space-between',
                height: chartHeight,
                marginBottom: theme.spacing.md,
                paddingLeft: theme.spacing.md,
                paddingRight: theme.spacing.md,
              }}>
                {config.data.slice(0, 6).map((item: any, index: number) => {
                  const height = (item.value / maxValue) * chartHeight;
                  const isHighlighted = index === config.data.length - 1;

                  return (
                    <div key={index} style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      flex: 1,
                      maxWidth: '60px',
                    }}>
                      <div style={{
                        fontSize: theme.typography.fontSize.xs,
                        color: theme.colors.text.secondary,
                        marginBottom: theme.spacing.xs,
                        fontWeight: theme.typography.fontWeight.medium,
                      }}>
                        {typeof item.value === 'number' ? `$${item.value}B` : item.value}
                      </div>
                      <div style={{
                        width: '100%',
                        height: `${height}px`,
                        backgroundColor: isHighlighted ? '#00cee6' : '#9b9bd7',
                        borderRadius: `${theme.borderRadius.sm}px ${theme.borderRadius.sm}px 0 0`,
                        marginBottom: theme.spacing.xs,
                        boxShadow: theme.shadows.sm,
                        transition: theme.transitions.fast,
                      }} />
                      <div style={{
                        fontSize: theme.typography.fontSize.xs,
                        color: theme.colors.text.secondary,
                        textAlign: 'center',
                        fontWeight: theme.typography.fontWeight.medium,
                      }}>
                        {item.year || item.month}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        }

        if (config.chartType === 'map') {
          return (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              padding: theme.spacing.lg,
              background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
              borderRadius: theme.borderRadius.lg,
            }}>
              <div style={{
                width: '100%',
                height: '100%',
                background: `
                  radial-gradient(circle at 30% 40%, #1976d2 8px, transparent 8px),
                  radial-gradient(circle at 70% 30%, #1976d2 6px, transparent 6px),
                  radial-gradient(circle at 50% 60%, #1976d2 10px, transparent 10px),
                  radial-gradient(circle at 20% 70%, #1976d2 7px, transparent 7px),
                  radial-gradient(circle at 80% 70%, #1976d2 9px, transparent 9px)
                `,
                borderRadius: theme.borderRadius.md,
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <div style={{
                  textAlign: 'center',
                  color: theme.colors.text.primary,
                  background: 'rgba(255, 255, 255, 0.9)',
                  padding: theme.spacing.md,
                  borderRadius: theme.borderRadius.md,
                  boxShadow: theme.shadows.sm,
                }}>
                  <div style={{ fontSize: theme.typography.fontSize.sm, fontWeight: theme.typography.fontWeight.semibold }}>
                    US States Map
                  </div>
                  <div style={{ fontSize: theme.typography.fontSize.xs, color: theme.colors.text.secondary, marginTop: theme.spacing.xs }}>
                    YTD % Change by State
                  </div>
                </div>
              </div>
            </div>
          );
        }

        return (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
            borderRadius: theme.borderRadius.lg,
            border: `2px dashed ${theme.colors.border.light}`,
          }}>
            <div style={{ textAlign: 'center', color: theme.colors.text.secondary }}>
              <div style={{ fontSize: '48px', marginBottom: theme.spacing.sm }}>📊</div>
              <div style={{ fontSize: theme.typography.fontSize.sm, fontWeight: theme.typography.fontWeight.medium }}>
                {title || 'Chart Placeholder'}
              </div>
              <div style={{ fontSize: theme.typography.fontSize.xs, marginTop: theme.spacing.xs, opacity: 0.7 }}>
                {config.chartType || 'Chart'} Visualization
              </div>
            </div>
          </div>
        );
      
      case 'table':
        const tableData = config.data || [
          { state: 'Texas', 2021: '$7,757,700,000', 2022: '$10,421,145,000', 2023: '$11,496,240,000', 2024: '$11,246,949,000', change: '+1%' },
          { state: 'California', 2021: '$6,406,400,000', 2022: '$6,240,481,000', 2023: '$6,434,754,000', 2024: '$6,167,493,000', change: '-3%' },
          { state: 'Florida', 2021: '$4,214,181,000', 2022: '$4,217,620,000', 2023: '$4,640,474,000', 2024: '$1,742,442,000', change: '-62%' },
          { state: 'New York', 2021: '$3,892,156,000', 2022: '$4,156,789,000', 2023: '$4,523,891,000', 2024: '$4,789,234,000', change: '+6%' },
          { state: 'Pennsylvania', 2021: '$2,567,890,000', 2022: '$2,789,456,000', 2023: '$3,012,345,000', 2024: '$3,234,567,000', change: '+7%' },
        ];
        const columns = config.columns || ['State', '2021', '2022', '2023', '2024', '%'];

        return (
          <div style={{
            height: '100%',
            overflow: 'auto',
            background: theme.colors.surface,
          }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              fontSize: theme.typography.fontSize.sm,
              background: theme.colors.surface,
            }}>
              <thead style={{
                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                position: 'sticky',
                top: 0,
                zIndex: 1,
              }}>
                <tr>
                  {columns.map((col, index) => (
                    <th key={index} style={{
                      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                      textAlign: index === 0 ? 'left' : 'right',
                      fontWeight: theme.typography.fontWeight.bold,
                      color: theme.colors.text.primary,
                      borderBottom: `3px solid ${theme.colors.primary}`,
                      fontSize: theme.typography.fontSize.sm,
                      letterSpacing: '0.5px',
                    }}>
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {tableData.map((row: any, rowIndex: number) => (
                  <tr key={rowIndex} style={{
                    borderBottom: `1px solid ${theme.colors.border.light}`,
                    backgroundColor: rowIndex % 2 === 0 ? theme.colors.surface : '#fafbfc',
                    transition: theme.transitions.fast,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f0f7ff';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = rowIndex % 2 === 0 ? theme.colors.surface : '#fafbfc';
                  }}
                  >
                    <td style={{
                      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                      fontWeight: theme.typography.fontWeight.semibold,
                      color: theme.colors.text.primary,
                    }}>
                      {row.state}
                    </td>
                    <td style={{
                      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                      textAlign: 'right',
                      fontFamily: 'monospace',
                      fontSize: theme.typography.fontSize.xs,
                    }}>
                      {row[2021]}
                    </td>
                    <td style={{
                      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                      textAlign: 'right',
                      fontFamily: 'monospace',
                      fontSize: theme.typography.fontSize.xs,
                    }}>
                      {row[2022]}
                    </td>
                    <td style={{
                      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                      textAlign: 'right',
                      fontFamily: 'monospace',
                      fontSize: theme.typography.fontSize.xs,
                    }}>
                      {row[2023]}
                    </td>
                    <td style={{
                      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                      textAlign: 'right',
                      fontFamily: 'monospace',
                      fontSize: theme.typography.fontSize.xs,
                      fontWeight: theme.typography.fontWeight.semibold,
                    }}>
                      {row[2024]}
                    </td>
                    <td style={{
                      padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                      textAlign: 'right',
                      color: row.change?.startsWith('+') ? '#22c55e' : '#ef4444',
                      fontWeight: theme.typography.fontWeight.bold,
                      fontSize: theme.typography.fontSize.sm,
                    }}>
                      {row.change}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'header':
        return (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            padding: theme.spacing.xl,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: theme.borderRadius.xl,
            color: 'white',
            height: '100%',
            position: 'relative',
            overflow: 'hidden',
          }}>
            <div style={{
              position: 'absolute',
              top: '-50px',
              right: '-50px',
              width: '150px',
              height: '150px',
              borderRadius: '50%',
              background: 'rgba(255, 255, 255, 0.1)',
            }} />
            <div style={{
              position: 'absolute',
              bottom: '-30px',
              left: '-30px',
              width: '100px',
              height: '100px',
              borderRadius: '50%',
              background: 'rgba(255, 255, 255, 0.05)',
            }} />

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: theme.spacing.lg,
              marginBottom: theme.spacing.lg,
              position: 'relative',
              zIndex: 1,
            }}>
              <div style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                background: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '24px',
                backdropFilter: 'blur(10px)',
              }}>
                👩‍💼
              </div>
              <div>
                <h3 style={{
                  margin: 0,
                  fontSize: theme.typography.fontSize.xl,
                  fontWeight: theme.typography.fontWeight.bold,
                  marginBottom: theme.spacing.xs,
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                }}>
                  {title}
                </h3>
                <div style={{
                  fontSize: theme.typography.fontSize.base,
                  fontWeight: theme.typography.fontWeight.medium,
                  opacity: 0.9,
                }}>
                  {config.economist || 'Dr. Alison Premo Black'}
                </div>
              </div>
            </div>

            <div style={{
              fontSize: theme.typography.fontSize.sm,
              lineHeight: '1.6',
              opacity: 0.9,
              position: 'relative',
              zIndex: 1,
            }}>
              {config.content || 'Contract Awards Dashboard analysis and insights for state and local government infrastructure investments. Tracking monthly, YTD, and TTM performance metrics across all transportation modes.'}
            </div>
          </div>
        );

      case 'info':
        return (
          <div style={{
            padding: theme.spacing.lg,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            background: theme.colors.gradients.surface,
            borderRadius: theme.borderRadius.lg,
            border: `1px solid ${theme.colors.border.light}`,
          }}>
            <h4 style={{
              margin: 0,
              fontSize: theme.typography.fontSize.base,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.md,
            }}>
              {title}
            </h4>
            <div style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
              marginBottom: theme.spacing.sm,
            }}>
              {config.content}
            </div>
            <div style={{
              fontSize: theme.typography.fontSize.xs,
              color: theme.colors.text.secondary,
              fontWeight: theme.typography.fontWeight.medium,
            }}>
              {config.details}
            </div>
          </div>
        );

      case 'filter':
        if (config.tabs) {
          return (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: theme.spacing.md,
              padding: theme.spacing.md,
              height: '100%',
            }}>
              {config.tabs.map((tab: string, index: number) => (
                <button
                  key={index}
                  style={{
                    padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                    borderRadius: theme.borderRadius.md,
                    border: 'none',
                    background: tab === config.activeTab ? theme.colors.primary : 'transparent',
                    color: tab === config.activeTab ? theme.colors.text.inverse : theme.colors.text.secondary,
                    fontSize: theme.typography.fontSize.sm,
                    fontWeight: theme.typography.fontWeight.medium,
                    cursor: 'pointer',
                    transition: theme.transitions.fast,
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          );
        }
        return (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}>
            Filter Controls
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <WidgetContainer title={title} position={position}>
      {getPlaceholderContent()}
    </WidgetContainer>
  );
};