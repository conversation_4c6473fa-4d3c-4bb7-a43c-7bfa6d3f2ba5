import React from 'react';
import { WidgetContainer } from '../../layout/WidgetContainer';
import { theme } from '../../../config/theme.config';
import type { WidgetPosition } from '../../../types/dashboard.types';

interface PlaceholderWidgetProps {
  title?: string;
  position: WidgetPosition;
  type: 'chart' | 'kpi' | 'table';
  widgetId: string;
}

export const PlaceholderWidget: React.FC<PlaceholderWidgetProps> = ({
  title,
  position,
  type,
  widgetId,
}) => {
  const getPlaceholderContent = () => {
    switch (type) {
      case 'kpi':
        return (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            padding: theme.spacing.md,
          }}>
            <div style={{
              fontSize: '36px',
              fontWeight: 700,
              color: theme.colors.primary,
              marginBottom: theme.spacing.sm,
            }}>
              $0.00
            </div>
            <div style={{
              fontSize: '14px',
              color: theme.colors.text.secondary,
            }}>
              {title || 'KPI Value'}
            </div>
          </div>
        );
      
      case 'chart':
        return (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            backgroundColor: theme.colors.background,
            borderRadius: theme.borderRadius.sm,
            border: `1px dashed ${theme.colors.border}`,
          }}>
            <div style={{ textAlign: 'center', color: theme.colors.text.secondary }}>
              <div style={{ fontSize: '48px', marginBottom: theme.spacing.sm }}>📊</div>
              <div style={{ fontSize: '14px' }}>Chart Placeholder</div>
              <div style={{ fontSize: '12px', marginTop: theme.spacing.xs, opacity: 0.7 }}>
                Widget ID: {widgetId}
              </div>
            </div>
          </div>
        );
      
      case 'table':
        return (
          <div style={{
            padding: theme.spacing.md,
            height: '100%',
            overflow: 'auto',
          }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: `2px solid ${theme.colors.border}` }}>
                  <th style={{ padding: theme.spacing.sm, textAlign: 'left' }}>Column 1</th>
                  <th style={{ padding: theme.spacing.sm, textAlign: 'left' }}>Column 2</th>
                  <th style={{ padding: theme.spacing.sm, textAlign: 'left' }}>Column 3</th>
                </tr>
              </thead>
              <tbody>
                {[1, 2, 3].map((row) => (
                  <tr key={row} style={{ borderBottom: `1px solid ${theme.colors.border}` }}>
                    <td style={{ padding: theme.spacing.sm }}>Data {row}-1</td>
                    <td style={{ padding: theme.spacing.sm }}>Data {row}-2</td>
                    <td style={{ padding: theme.spacing.sm }}>Data {row}-3</td>
                  </tr>
                ))}
              </tbody>
            </table>
            <div style={{
              fontSize: '12px',
              color: theme.colors.text.secondary,
              marginTop: theme.spacing.md,
              textAlign: 'center',
            }}>
              Widget ID: {widgetId}
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <WidgetContainer title={title} position={position}>
      {getPlaceholderContent()}
    </WidgetContainer>
  );
};