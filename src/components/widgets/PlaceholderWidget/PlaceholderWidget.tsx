import React from 'react';
import { WidgetContainer } from '../../layout/WidgetContainer';
import { theme } from '../../../config/theme.config';
import type { WidgetPosition } from '../../../types/dashboard.types';

interface PlaceholderWidgetProps {
  title?: string;
  position: WidgetPosition;
  type: 'chart' | 'kpi' | 'table' | 'filter' | 'header' | 'info' | 'text';
  widgetId: string;
  config?: any;
}

export const PlaceholderWidget: React.FC<PlaceholderWidgetProps> = ({
  title,
  position,
  type,
  widgetId,
  config = {},
}) => {
  const getPlaceholderContent = () => {
    switch (type) {
      case 'kpi':
        return (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            padding: theme.spacing.lg,
            background: config.color ? `linear-gradient(135deg, ${config.color}10, ${config.color}05)` : 'transparent',
          }}>
            <div style={{
              fontSize: position.w > 2 ? '48px' : '32px',
              fontWeight: theme.typography.fontWeight.bold,
              color: config.color || theme.colors.primary,
              marginBottom: theme.spacing.sm,
              textAlign: 'center',
            }}>
              {config.value || '$0.00'}
            </div>
            <div style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
              textAlign: 'center',
              marginBottom: theme.spacing.xs,
            }}>
              {title || 'KPI Value'}
            </div>
            {config.subtitle && (
              <div style={{
                fontSize: theme.typography.fontSize.xs,
                color: theme.colors.text.secondary,
                textAlign: 'center',
                opacity: 0.8,
              }}>
                {config.subtitle}
              </div>
            )}
          </div>
        );
      
      case 'chart':
        if (config.chartType === 'pie' && config.data) {
          return (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              padding: theme.spacing.lg,
            }}>
              <div style={{
                width: '120px',
                height: '120px',
                borderRadius: '50%',
                background: `conic-gradient(${config.data.map((item: any, index: number) =>
                  `${item.color || theme.colors.primary} ${index * (360 / config.data.length)}deg ${(index + 1) * (360 / config.data.length)}deg`
                ).join(', ')})`,
                position: 'relative',
              }}>
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  background: theme.colors.surface,
                  borderRadius: '50%',
                  width: '60px',
                  height: '60px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: theme.typography.fontSize.sm,
                  fontWeight: theme.typography.fontWeight.semibold,
                }}>
                  {config.data[0]?.value}%
                </div>
              </div>
            </div>
          );
        }

        if (config.chartType === 'column' && config.data) {
          return (
            <div style={{
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              height: '100%',
              padding: theme.spacing.lg,
              gap: theme.spacing.sm,
            }}>
              {config.data.slice(0, 6).map((item: any, index: number) => (
                <div key={index} style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  flex: 1,
                }}>
                  <div style={{
                    width: '100%',
                    height: `${(item.value / Math.max(...config.data.map((d: any) => d.value))) * 100}px`,
                    backgroundColor: theme.colors.primary,
                    borderRadius: `${theme.borderRadius.xs}px ${theme.borderRadius.xs}px 0 0`,
                    marginBottom: theme.spacing.xs,
                  }} />
                  <div style={{
                    fontSize: theme.typography.fontSize.xs,
                    color: theme.colors.text.secondary,
                    textAlign: 'center',
                  }}>
                    {item.year || item.month}
                  </div>
                </div>
              ))}
            </div>
          );
        }

        return (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            backgroundColor: theme.colors.backgroundGradient,
            borderRadius: theme.borderRadius.sm,
            border: `1px dashed ${theme.colors.border.light}`,
          }}>
            <div style={{ textAlign: 'center', color: theme.colors.text.secondary }}>
              <div style={{ fontSize: '48px', marginBottom: theme.spacing.sm }}>📊</div>
              <div style={{ fontSize: theme.typography.fontSize.sm }}>Chart Placeholder</div>
              <div style={{ fontSize: theme.typography.fontSize.xs, marginTop: theme.spacing.xs, opacity: 0.7 }}>
                {title}
              </div>
            </div>
          </div>
        );
      
      case 'table':
        const tableData = config.data || [
          { state: 'Texas', 2021: '$7.7B', 2022: '$10.4B', 2023: '$11.5B', 2024: '$11.2B', change: '+1%' },
          { state: 'California', 2021: '$6.4B', 2022: '$6.2B', 2023: '$6.4B', 2024: '$6.2B', change: '-3%' },
          { state: 'Florida', 2021: '$4.2B', 2022: '$4.2B', 2023: '$4.6B', 2024: '$1.7B', change: '-62%' },
        ];
        const columns = config.columns || ['State', '2021', '2022', '2023', '2024', '%'];

        return (
          <div style={{
            padding: theme.spacing.lg,
            height: '100%',
            overflow: 'auto',
          }}>
            <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: theme.typography.fontSize.sm }}>
              <thead>
                <tr style={{ borderBottom: `2px solid ${theme.colors.border.medium}` }}>
                  {columns.map((col, index) => (
                    <th key={index} style={{
                      padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                      textAlign: index === 0 ? 'left' : 'right',
                      fontWeight: theme.typography.fontWeight.semibold,
                      color: theme.colors.text.primary,
                    }}>
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {tableData.map((row: any, rowIndex: number) => (
                  <tr key={rowIndex} style={{
                    borderBottom: `1px solid ${theme.colors.border.light}`,
                    '&:hover': { backgroundColor: theme.colors.surfaceHover }
                  }}>
                    <td style={{
                      padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                      fontWeight: theme.typography.fontWeight.medium,
                    }}>
                      {row.state}
                    </td>
                    <td style={{ padding: `${theme.spacing.sm}px ${theme.spacing.md}px`, textAlign: 'right' }}>{row[2021]}</td>
                    <td style={{ padding: `${theme.spacing.sm}px ${theme.spacing.md}px`, textAlign: 'right' }}>{row[2022]}</td>
                    <td style={{ padding: `${theme.spacing.sm}px ${theme.spacing.md}px`, textAlign: 'right' }}>{row[2023]}</td>
                    <td style={{ padding: `${theme.spacing.sm}px ${theme.spacing.md}px`, textAlign: 'right' }}>{row[2024]}</td>
                    <td style={{
                      padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
                      textAlign: 'right',
                      color: row.change?.startsWith('+') ? theme.colors.success : theme.colors.error,
                      fontWeight: theme.typography.fontWeight.semibold,
                    }}>
                      {row.change}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'header':
        return (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing.lg,
            padding: theme.spacing.lg,
            background: theme.colors.gradients.cool,
            borderRadius: theme.borderRadius.lg,
            color: theme.colors.text.inverse,
            height: '100%',
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              background: 'rgba(255, 255, 255, 0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '32px',
            }}>
              👩‍💼
            </div>
            <div>
              <h3 style={{
                margin: 0,
                fontSize: theme.typography.fontSize.lg,
                fontWeight: theme.typography.fontWeight.semibold,
                marginBottom: theme.spacing.xs,
              }}>
                {title}
              </h3>
              <div style={{
                fontSize: theme.typography.fontSize.base,
                fontWeight: theme.typography.fontWeight.medium,
                marginBottom: theme.spacing.xs,
              }}>
                {config.economist || 'Dr. Alison Premo Black'}
              </div>
              <div style={{
                fontSize: theme.typography.fontSize.sm,
                opacity: 0.9,
              }}>
                {config.content || 'Economic analysis and insights'}
              </div>
            </div>
          </div>
        );

      case 'info':
        return (
          <div style={{
            padding: theme.spacing.lg,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            background: theme.colors.gradients.surface,
            borderRadius: theme.borderRadius.lg,
            border: `1px solid ${theme.colors.border.light}`,
          }}>
            <h4 style={{
              margin: 0,
              fontSize: theme.typography.fontSize.base,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.md,
            }}>
              {title}
            </h4>
            <div style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
              marginBottom: theme.spacing.sm,
            }}>
              {config.content}
            </div>
            <div style={{
              fontSize: theme.typography.fontSize.xs,
              color: theme.colors.text.secondary,
              fontWeight: theme.typography.fontWeight.medium,
            }}>
              {config.details}
            </div>
          </div>
        );

      case 'filter':
        if (config.tabs) {
          return (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: theme.spacing.md,
              padding: theme.spacing.md,
              height: '100%',
            }}>
              {config.tabs.map((tab: string, index: number) => (
                <button
                  key={index}
                  style={{
                    padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                    borderRadius: theme.borderRadius.md,
                    border: 'none',
                    background: tab === config.activeTab ? theme.colors.primary : 'transparent',
                    color: tab === config.activeTab ? theme.colors.text.inverse : theme.colors.text.secondary,
                    fontSize: theme.typography.fontSize.sm,
                    fontWeight: theme.typography.fontWeight.medium,
                    cursor: 'pointer',
                    transition: theme.transitions.fast,
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          );
        }
        return (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.sm,
          }}>
            Filter Controls
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <WidgetContainer title={title} position={position}>
      {getPlaceholderContent()}
    </WidgetContainer>
  );
};