import React from 'react';
import { WidgetById } from '@sisense/sdk-ui';
import { WidgetContainer } from '../../layout/WidgetContainer';
import type { WidgetPosition } from '../../../types/dashboard.types';

interface SisenseWidgetProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
}

export const SisenseWidget: React.FC<SisenseWidgetProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
}) => {
  return (
    <WidgetContainer title={title} position={position}>
      <WidgetById
        widgetOid={widgetId}
        dashboardOid={dashboardId}
      />
    </WidgetContainer>
  );
};