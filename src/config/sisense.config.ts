// Sisense configuration
// Authentication will be implemented later

export const sisenseConfig = {
  url: import.meta.env.VITE_SISENSE_URL || 'https://your-sisense-instance.com',
  defaultDataSource: import.meta.env.VITE_SISENSE_DATA_SOURCE || 'Sample ECommerce',
  // Authentication config will be added later
  // token: import.meta.env.VITE_SISENSE_TOKEN,
};

import { createAttribute } from '@sisense/sdk-data';

// Placeholder data source - replace with your actual data model
export const DM = {
  Commerce: {
    Date: createAttribute({
      name: 'Date',
      type: 'datetime',
      expression: '[Commerce.Date]',
    }),
    Revenue: createAttribute({
      name: 'Revenue',
      type: 'numeric',
      expression: '[Commerce.Revenue]',
    }),
    Quantity: createAttribute({
      name: 'Quantity',
      type: 'numeric',
      expression: '[Commerce.Quantity]',
    }),
    Category: createAttribute({
      name: 'Category',
      type: 'text',
      expression: '[Commerce.Category]',
    }),
    Product: createAttribute({
      name: 'Product',
      type: 'text',
      expression: '[Commerce.Product]',
    }),
    Customer: createAttribute({
      name: 'Customer',
      type: 'text',
      expression: '[Commerce.Customer]',
    }),
    Country: createAttribute({
      name: 'Country',
      type: 'text',
      expression: '[Commerce.Country]',
    }),
  },
};