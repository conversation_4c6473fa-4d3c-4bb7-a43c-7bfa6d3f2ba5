import React from 'react';
import { useNavigate } from 'react-router-dom';
import { theme } from '../../config/theme.config';

interface DashboardCardProps {
  title: string;
  description: string;
  path: string;
  icon?: string;
  color?: string;
}

export const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  description,
  path,
  icon,
  color = theme.colors.primary,
}) => {
  const navigate = useNavigate();

  return (
    <div
      onClick={() => navigate(path)}
      style={{
        backgroundColor: theme.colors.surface,
        borderRadius: theme.borderRadius.card,
        boxShadow: theme.shadows.widget,
        padding: theme.spacing.lg,
        cursor: 'pointer',
        transition: `all ${theme.transitions.normal}`,
        border: '2px solid transparent',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.md;
        e.currentTarget.style.borderColor = color;
        e.currentTarget.style.transform = 'translateY(-2px)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.widget;
        e.currentTarget.style.borderColor = 'transparent';
        e.currentTarget.style.transform = 'translateY(0)';
      }}
    >
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing.md,
        marginBottom: theme.spacing.md,
      }}>
        {icon && (
          <div style={{
            width: 48,
            height: 48,
            backgroundColor: color,
            borderRadius: theme.borderRadius.md,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '24px',
            color: 'white',
          }}>
            {icon}
          </div>
        )}
        <h3 style={{
          margin: 0,
          color: theme.colors.text.primary,
          fontSize: '20px',
          fontWeight: 600,
        }}>
          {title}
        </h3>
      </div>
      <p style={{
        margin: 0,
        color: theme.colors.text.secondary,
        fontSize: '14px',
        lineHeight: 1.5,
        flex: 1,
      }}>
        {description}
      </p>
      <div style={{
        marginTop: theme.spacing.md,
        color: color,
        fontSize: '14px',
        fontWeight: 500,
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing.xs,
      }}>
        Open Dashboard →
      </div>
    </div>
  );
};