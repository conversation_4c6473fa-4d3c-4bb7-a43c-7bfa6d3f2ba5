import React from 'react';
import { useNavigate } from 'react-router-dom';
import { PageLayout } from '../../components/layout/PageLayout';
import { DashboardCard } from './DashboardCard';
import { theme } from '../../config/theme.config';

// These dashboards are extracted from the .dash files
const dashboards = [
  {
    id: 'summary',
    title: '1. Summary Dashboard',
    description: 'Executive summary of key transportation metrics including contract awards, federal obligations, and material prices.',
    path: '/dashboard/summary',
    icon: '📊',
    color: theme.colors.primary,
  },
  {
    id: 'contract-awards',
    title: '2. Contract Awards',
    description: 'Track state and local contract awards with TTM and YTD metrics, geographic distribution, and trending analysis.',
    path: '/dashboard/contract-awards',
    icon: '📄',
    color: '#00cee6',
  },
  {
    id: 'value-put-in-place',
    title: '3. Value Put in Place',
    description: 'Monitor construction value put in place across highways, bridges, and other infrastructure projects.',
    path: '/dashboard/value-put-in-place',
    icon: '🏗️',
    color: '#9b9bd7',
  },
  {
    id: 'federal-aid',
    title: '4. Federal-Aid Obligations',
    description: 'Track federal highway aid obligations by state and program, with historical trends and distributions.',
    path: '/dashboard/federal-aid',
    icon: '🏛️',
    color: '#6EDA55',
  },
  {
    id: 'state-legislative',
    title: '6. State Legislative Initiatives',
    description: 'Monitor state transportation funding initiatives, ballot measures, and legislative actions.',
    path: '/dashboard/state-legislative-initiatives',
    icon: '📜',
    color: '#fc7570',
  },
  {
    id: 'state-dot-budgets',
    title: '7. State DOT Budgets 2025',
    description: 'Analyze state department of transportation budgets, funding sources, and year-over-year changes.',
    path: '/dashboard/state-dot-budgets',
    icon: '💰',
    color: '#fbb755',
  },
  {
    id: 'material-prices',
    title: '9. Material Prices',
    description: 'Track construction material price indices for asphalt, concrete, steel, and aggregates with trend analysis.',
    path: '/dashboard/material-prices',
    icon: '📈',
    color: '#218A8C',
  },
];

export const Landing: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <PageLayout title="Analytics Dashboard">
      <div>
        <div style={{
          textAlign: 'center',
          marginBottom: theme.spacing.xl * 2,
        }}>
          <h1 style={{
            fontSize: '36px',
            fontWeight: 700,
            color: theme.colors.text.primary,
            margin: 0,
            marginBottom: theme.spacing.md,
          }}>
            ARTBA Economics Dashboard
          </h1>
          <p style={{
            fontSize: '18px',
            color: theme.colors.text.secondary,
            maxWidth: '600px',
            margin: '0 auto',
          }}>
            Transportation construction industry analytics and economic indicators
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
          gap: theme.spacing.lg,
          marginBottom: theme.spacing.xl,
        }}>
          {dashboards.map((dashboard) => (
            <DashboardCard
              key={dashboard.id}
              title={dashboard.title}
              description={dashboard.description}
              path={dashboard.path}
              icon={dashboard.icon}
              color={dashboard.color}
            />
          ))}
        </div>

        <div style={{
          backgroundColor: theme.colors.surface,
          borderRadius: theme.borderRadius.card,
          padding: theme.spacing.xl,
          textAlign: 'center',
          marginTop: theme.spacing.xl * 2,
        }}>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 600,
            color: theme.colors.text.primary,
            marginBottom: theme.spacing.md,
          }}>
            Need Help?
          </h2>
          <p style={{
            color: theme.colors.text.secondary,
            marginBottom: theme.spacing.lg,
          }}>
            Check out our documentation or contact support for assistance
          </p>
          <div style={{
            display: 'flex',
            gap: theme.spacing.md,
            justifyContent: 'center',
          }}>
            <button
              onClick={() => navigate('/import')}
              style={{
                backgroundColor: theme.colors.primary,
                color: 'white',
                border: 'none',
                borderRadius: theme.borderRadius.md,
                padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                fontSize: '16px',
                fontWeight: 500,
                cursor: 'pointer',
                transition: `background-color ${theme.transitions.fast}`,
              }}
            >
              Import Dashboard
            </button>
            <button style={{
              backgroundColor: 'transparent',
              color: theme.colors.primary,
              border: `2px solid ${theme.colors.primary}`,
              borderRadius: theme.borderRadius.md,
              padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
              fontSize: '16px',
              fontWeight: 500,
              cursor: 'pointer',
              transition: `all ${theme.transitions.fast}`,
            }}>
              View Documentation
            </button>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};