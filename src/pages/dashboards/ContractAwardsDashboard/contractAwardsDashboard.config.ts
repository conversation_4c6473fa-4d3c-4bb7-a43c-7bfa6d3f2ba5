import type { DashboardConfig } from '../../../types/dashboard.types';

export const contractAwardsDashboardConfig: DashboardConfig = {
  id: '5defb5024faf0a20b09a2141',
  title: '2. Contract Awards',
  description: 'Contract awards tracking and analysis',
  path: '/dashboard/contract-awards',
  widgets: [
    // Placeholder widgets (empty titles)
    {
      id: '5defb5024faf0a20b09a2143',
      type: 'filter',
      title: '',
      position: { x: 0, y: 0, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5defb5024faf0a20b09a2144',
      type: 'filter',
      title: '',
      position: { x: 2, y: 0, w: 2, h: 1 },
      config: {},
    },
    // Rich text widgets
    {
      id: '5defb5024faf0a20b09a2146',
      type: 'kpi',
      title: 'RICHTEXT_MAIN.TITLE',
      position: { x: 4, y: 0, w: 4, h: 2 },
      config: {
        value: 'Contract Awards Dashboard',
        format: 'text',
      },
    },
    {
      id: '5defb5024faf0a20b09a2148',
      type: 'filter',
      title: '',
      position: { x: 8, y: 0, w: 4, h: 1 },
      config: {},
    },
    // Row 1 - Monthly metrics
    {
      id: '5df106fc1d1da61c08f09137',
      type: 'chart',
      title: 'Monthly Value',
      position: { x: 0, y: 2, w: 4, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Month' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defb5024faf0a20b09a2149',
      type: 'chart',
      title: 'Monthly Value by State',
      position: { x: 4, y: 2, w: 4, h: 4 },
      config: {
        chartType: 'treemap',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e20c65b836b0701b460503b',
      type: 'chart',
      title: 'Monthly Value by Mode',
      position: { x: 8, y: 2, w: 4, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 2 - YTD metrics
    {
      id: '5defb5024faf0a20b09a214a',
      type: 'kpi',
      title: 'YTD Value',
      position: { x: 0, y: 6, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '5defb5024faf0a20b09a214b',
      type: 'chart',
      title: 'YTD Value by Mode',
      position: { x: 3, y: 6, w: 4, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defb5024faf0a20b09a214f',
      type: 'chart',
      title: 'YTD Value by State',
      position: { x: 7, y: 6, w: 5, h: 4 },
      config: {
        chartType: 'treemap',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Additional placeholder widgets
    {
      id: '5defb5024faf0a20b09a214d',
      type: 'filter',
      title: '',
      position: { x: 0, y: 8, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5defb5024faf0a20b09a214e',
      type: 'filter',
      title: '',
      position: { x: 2, y: 8, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5defb5024faf0a20b09a2151',
      type: 'filter',
      title: '',
      position: { x: 4, y: 8, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5defb5024faf0a20b09a2152',
      type: 'filter',
      title: '',
      position: { x: 6, y: 8, w: 2, h: 1 },
      config: {},
    },
    // Row 3 - Contract award counts
    {
      id: '5defb5024faf0a20b09a214c',
      type: 'chart',
      title: 'Monthly Number of Contract Awards',
      position: { x: 0, y: 10, w: 4, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Month' }],
          value: [{ name: 'Count', aggregation: 'count' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defb5024faf0a20b09a2153',
      type: 'kpi',
      title: 'YTD Number of Contract Awards',
      position: { x: 4, y: 10, w: 4, h: 2 },
      config: {
        value: 0,
        format: 'number',
      },
    },
    {
      id: '5e597648b882c5081c3d1d42',
      type: 'chart',
      title: 'Annual Number of Contract Awards',
      position: { x: 8, y: 10, w: 4, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [{ name: 'Count', aggregation: 'count' }],
          breakBy: [],
        },
      },
    },
    // Additional placeholder widgets
    {
      id: '5defb5024faf0a20b09a2154',
      type: 'filter',
      title: '',
      position: { x: 0, y: 14, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5defb5024faf0a20b09a2157',
      type: 'filter',
      title: '',
      position: { x: 2, y: 14, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5defb5024faf0a20b09a2159',
      type: 'filter',
      title: '',
      position: { x: 4, y: 14, w: 2, h: 1 },
      config: {},
    },
    // Row 4 - TTM metrics
    {
      id: '5defb5024faf0a20b09a2155',
      type: 'kpi',
      title: 'TTM Value',
      position: { x: 0, y: 16, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '5defb5024faf0a20b09a2156',
      type: 'kpi',
      title: 'TTM Number of Contract Awards',
      position: { x: 3, y: 16, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'number',
      },
    },
    {
      id: '5e20c699836b0701b4605041',
      type: 'chart',
      title: 'TTM Value by Mode',
      position: { x: 6, y: 16, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 5 - State analysis
    {
      id: '5defb5024faf0a20b09a2158',
      type: 'chart',
      title: 'TTM Value of Contract Awards by State',
      position: { x: 0, y: 20, w: 6, h: 4 },
      config: {
        chartType: 'scatter',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e5977c9b882c5081c3d1e01',
      type: 'chart',
      title: '2024 Value by State',
      position: { x: 6, y: 20, w: 6, h: 4 },
      config: {
        chartType: 'treemap',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 6 - Annual metrics
    {
      id: '5e5975da52e4360b0027b1ea',
      type: 'chart',
      title: 'Annual Value',
      position: { x: 0, y: 24, w: 6, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e5974fab882c5081c3d1d3a',
      type: 'chart',
      title: '2024 Annual Value by Mode',
      position: { x: 6, y: 24, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Additional widgets from the list
    {
      id: '5df10ba31d1da61c08f09139',
      type: 'filter',
      title: '',
      position: { x: 0, y: 28, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5e20c155836b0701b4605022',
      type: 'filter',
      title: '',
      position: { x: 2, y: 28, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5e20c1ca836b0701b4605028',
      type: 'filter',
      title: '',
      position: { x: 4, y: 28, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5e3d8dc03c4403190ccca9ee',
      type: 'filter',
      title: '',
      position: { x: 6, y: 28, w: 2, h: 1 },
      config: {},
    },
    // Rich text widgets
    {
      id: '5e44661f3c4403190cccafd8',
      type: 'kpi',
      title: 'RICHTEXT_MAIN.TITLE',
      position: { x: 8, y: 28, w: 4, h: 2 },
      config: {
        value: 'Contract Awards Analysis',
        format: 'text',
      },
    },
    {
      id: '5e44664b3c4403190cccafda',
      type: 'kpi',
      title: 'RICHTEXT_MAIN.TITLE',
      position: { x: 0, y: 30, w: 4, h: 2 },
      config: {
        value: 'Performance Metrics',
        format: 'text',
      },
    },
    // Additional placeholder widgets
    {
      id: '5e5973a5b882c5081c3d1d22',
      type: 'filter',
      title: '',
      position: { x: 4, y: 30, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5e597432b882c5081c3d1d34',
      type: 'filter',
      title: '',
      position: { x: 6, y: 30, w: 2, h: 1 },
      config: {},
    },
    {
      id: '5e597682b882c5081c3d1df9',
      type: 'filter',
      title: '',
      position: { x: 8, y: 30, w: 2, h: 1 },
      config: {},
    },
  ],
};