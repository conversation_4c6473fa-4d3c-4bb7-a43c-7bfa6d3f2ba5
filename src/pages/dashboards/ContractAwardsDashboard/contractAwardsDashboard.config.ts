import type { DashboardConfig } from '../../../types/dashboard.types';

export const contractAwardsDashboardConfig: DashboardConfig = {
  id: '5defb5024faf0a20b09a2141',
  title: '2. Contract Awards',
  description: 'Contract awards tracking and analysis',
  path: '/dashboard/contract-awards',
  widgets: [
    // Row 1 - Monthly metrics
    {
      id: '5df106fc1d1da61c08f09137',
      type: 'chart',
      title: 'Monthly Value',
      position: { x: 0, y: 0, w: 4, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Month' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defb5024faf0a20b09a2149',
      type: 'chart',
      title: 'Monthly Value by State',
      position: { x: 4, y: 0, w: 4, h: 4 },
      config: {
        chartType: 'treemap',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e20c65b836b0701b460503b',
      type: 'chart',
      title: 'Monthly Value by Mode',
      position: { x: 8, y: 0, w: 4, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 2 - YTD metrics
    {
      id: '5defb5024faf0a20b09a214a',
      type: 'kpi',
      title: 'YTD Value',
      position: { x: 0, y: 4, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '5defb5024faf0a20b09a214b',
      type: 'chart',
      title: 'YTD Value by Mode',
      position: { x: 3, y: 4, w: 4, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defb5024faf0a20b09a214f',
      type: 'chart',
      title: 'YTD Value by State',
      position: { x: 7, y: 4, w: 5, h: 4 },
      config: {
        chartType: 'treemap',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 3 - Contract award counts
    {
      id: '5defb5024faf0a20b09a214c',
      type: 'chart',
      title: 'Monthly Number of Contract Awards',
      position: { x: 0, y: 8, w: 4, h: 4 },
      config: {
        chartType: 'line',
        dataOptions: {
          category: [{ name: 'Month' }],
          value: [{ name: 'Count', aggregation: 'count' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5defb5024faf0a20b09a2153',
      type: 'kpi',
      title: 'YTD Number of Contract Awards',
      position: { x: 4, y: 8, w: 4, h: 2 },
      config: {
        value: 0,
        format: 'number',
      },
    },
    {
      id: '5e597648b882c5081c3d1d42',
      type: 'chart',
      title: 'Annual Number of Contract Awards',
      position: { x: 8, y: 8, w: 4, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [{ name: 'Count', aggregation: 'count' }],
          breakBy: [],
        },
      },
    },
    // Row 4 - TTM metrics
    {
      id: '5defb5024faf0a20b09a2155',
      type: 'kpi',
      title: 'TTM Value',
      position: { x: 0, y: 12, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'currency',
        prefix: '$',
      },
    },
    {
      id: '5defb5024faf0a20b09a2156',
      type: 'kpi',
      title: 'TTM Number of Contract Awards',
      position: { x: 3, y: 12, w: 3, h: 2 },
      config: {
        value: 0,
        format: 'number',
      },
    },
    {
      id: '5e20c699836b0701b4605041',
      type: 'chart',
      title: 'TTM Value by Mode',
      position: { x: 6, y: 12, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 5 - State analysis
    {
      id: '5defb5024faf0a20b09a2158',
      type: 'chart',
      title: 'TTM Value of Contract Awards by State',
      position: { x: 0, y: 16, w: 6, h: 4 },
      config: {
        chartType: 'scatter',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e5977c9b882c5081c3d1e01',
      type: 'chart',
      title: '2024 Value by State',
      position: { x: 6, y: 16, w: 6, h: 4 },
      config: {
        chartType: 'treemap',
        dataOptions: {
          category: [{ name: 'State' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    // Row 6 - Annual metrics
    {
      id: '5e5975da52e4360b0027b1ea',
      type: 'chart',
      title: 'Annual Value',
      position: { x: 0, y: 20, w: 6, h: 4 },
      config: {
        chartType: 'column',
        dataOptions: {
          category: [{ name: 'Year' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
    {
      id: '5e5974fab882c5081c3d1d3a',
      type: 'chart',
      title: '2024 Annual Value by Mode',
      position: { x: 6, y: 20, w: 6, h: 4 },
      config: {
        chartType: 'pie',
        dataOptions: {
          category: [{ name: 'Mode' }],
          value: [{ name: 'Value', aggregation: 'sum' }],
          breakBy: [],
        },
      },
    },
  ],
};