import type { DashboardConfig } from '../../../types/dashboard.types';

export const contractAwardsDashboardConfig: DashboardConfig = {
  id: '5defb5024faf0a20b09a2141',
  title: 'Contract Awards',
  description: 'State & Local Government Contract Awards with TTM and YTD metrics, geographic distribution, and trending analysis',
  path: '/dashboard/contract-awards',
  widgets: [
    // Header Section - Chief Economist Profile
    {
      id: '5defb5024faf0a20b09a2146',
      type: 'header',
      title: 'From the Chief Economist',
      position: { x: 0, y: 0, w: 4, h: 3 },
      config: {
        economist: 'Dr. <PERSON>',
        image: '/economist-profile.jpg',
        content: 'Contract Awards Dashboard analysis and insights',
      },
    },
    // Top KPI Row - Monthly, YTD, TTM Values
    {
      id: '5df106fc1d1da61c08f09137',
      title: 'Monthly Value',
      position: { x: 4, y: 0, w: 2, h: 2 },
      config: {
        value: '$10.9B',
        subtitle: 'Number of Projects: 4,136',
        color: '#00cee6',
      },
    },
    {
      id: '5defb5024faf0a20b09a214a',
      type: 'kpi',
      title: 'YTD Value',
      position: { x: 6, y: 0, w: 2, h: 2 },
      config: {
        value: '$54.8B',
        subtitle: 'Number of Projects: 15,191',
        color: '#9b9bd7',
      },
    },
    {
      id: '5defb5024faf0a20b09a2155',
      type: 'kpi',
      title: 'TTM Value',
      position: { x: 8, y: 0, w: 2, h: 2 },
      config: {
        value: '$124.2B',
        subtitle: 'Number of Projects: 38,711',
        color: '#6EDA55',
      },
    },
    // Data Table Section
    {
      id: '5defb5024faf0a20b09a2148',
      type: 'info',
      title: 'State & Local Govt Contract Awards',
      position: { x: 10, y: 0, w: 2, h: 3 },
      config: {
        content: 'May 2025 Data',
        details: 'Contract Awards Total $13.3B in May, YTD 2025 Value of Awards Up 11%',
      },
    },
    // Navigation Tabs
    {
      id: '5defb5024faf0a20b09a2143',
      type: 'filter',
      title: '',
      position: { x: 0, y: 3, w: 12, h: 1 },
      config: {
        tabs: ['MONTH', 'YTD', 'TTM', 'ANNUAL'],
        activeTab: 'YTD',
      },
    },
    // Main Data Table
    {
      id: '5defb5024faf0a20b09a214f',
      type: 'table',
      title: 'YTD Value by State',
      position: { x: 0, y: 4, w: 6, h: 8 },
      config: {
        data: [
          { state: 'Texas', 2021: '$7,757,700,000', 2022: '$10,421,145,000', 2023: '$11,496,240,000', 2024: '$11,246,949,000' },
          { state: 'California', 2021: '$6,406,400,000', 2022: '$6,240,481,000', 2023: '$6,434,754,000', 2024: '$6,167,493,000' },
          { state: 'Florida', 2021: '$4,214,181,000', 2022: '$4,217,620,000', 2023: '$4,640,474,000', 2024: '$1,742,442,000' },
        ],
        columns: ['State', '2021', '2022', '2023', '2024', '%'],
      },
    },
    // YTD Value by Mode Pie Chart
    {
      id: '5defb5024faf0a20b09a214b',
      type: 'chart',
      title: 'YTD Value by Mode',
      position: { x: 6, y: 4, w: 3, h: 4 },
      config: {
        chartType: 'pie',
        data: [
          { mode: 'Bridge & Tunnel', value: 76, color: '#00cee6' },
          { mode: 'Highway & Pavement', value: 24, color: '#9b9bd7' },
        ],
      },
    },
    // YTD Value Bar Chart
    {
      id: '5e5975da52e4360b0027b1ea',
      type: 'chart',
      title: 'YTD Value',
      position: { x: 9, y: 4, w: 3, h: 4 },
      config: {
        chartType: 'column',
        data: [
          { year: '2020', value: 40.0 },
          { year: '2021', value: 42.0 },
          { year: '2022', value: 45.0 },
          { year: '2023', value: 50.0 },
          { year: '2024', value: 55.0 },
          { year: '2025', value: 54.8 },
        ],
      },
    },
    // US Map Visualization
    {
      id: '5defb5024faf0a20b09a2158',
      type: 'chart',
      title: 'YTD % Change',
      position: { x: 0, y: 8, w: 6, h: 6 },
      config: {
        chartType: 'map',
        data: 'us-states',
        valueField: 'ytdChange',
        colorScale: ['#e3f2fd', '#1976d2'],
      },
    },
    // YTD Number of Contract Awards
    {
      id: '5defb5024faf0a20b09a2153',
      type: 'chart',
      title: 'YTD Number of Contract Awards',
      position: { x: 6, y: 8, w: 6, h: 6 },
      config: {
        chartType: 'column',
        data: [
          { year: '2020', value: 15000 },
          { year: '2021', value: 16000 },
          { year: '2022', value: 17000 },
          { year: '2023', value: 18000 },
          { year: '2024', value: 19000 },
          { year: '2025', value: 17500 },
        ],
      },
    },
  ],
};