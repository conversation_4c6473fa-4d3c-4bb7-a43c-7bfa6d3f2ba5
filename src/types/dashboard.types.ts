export interface WidgetPosition {
  x: number;
  y: number;
  w: number;
  h: number;
}

export type WidgetType = 'chart' | 'kpi' | 'table' | 'filter' | 'pivot';

export interface WidgetConfig {
  id: string;
  type: WidgetType;
  title: string;
  position: WidgetPosition;
  config: any;
}

export interface DashboardConfig {
  id: string;
  title: string;
  description: string;
  icon?: string;
  path: string;
  widgets: WidgetConfig[];
}