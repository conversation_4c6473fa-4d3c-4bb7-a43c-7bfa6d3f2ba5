import type { ChartDataOptions, TableDataOptions, ChartType } from '@sisense/sdk-ui';

export interface BaseWidgetProps {
  id: string;
  title?: string;
  description?: string;
  className?: string;
}

export interface ChartWidgetProps extends BaseWidgetProps {
  chartType: ChartType | string;
  dataOptions: ChartDataOptions;
  dataSource?: any;
  filters?: any[];
  highlights?: any[];
  onDataPointClick?: (point: any, event: any) => void;
}

export interface KPIWidgetProps extends BaseWidgetProps {
  value: number | string;
  previousValue?: number | string;
  format?: 'number' | 'currency' | 'percentage';
  trend?: 'up' | 'down' | 'neutral';
  icon?: React.ReactNode;
}

export interface TableWidgetProps extends BaseWidgetProps {
  dataOptions: TableDataOptions;
  dataSet?: any;
  filters?: any[];
}