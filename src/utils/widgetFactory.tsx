import React from 'react';
import { SisenseWidget } from '../components/widgets/SisenseWidget/SisenseWidget';
import { PlaceholderWidget } from '../components/widgets/PlaceholderWidget/PlaceholderWidget';
import { ErrorBoundary } from '../components/ErrorBoundary/ErrorBoundary';
import type { WidgetConfig } from '../types/dashboard.types';

// Check if we're in development mode without Sisense connection
const isDevelopmentMode = import.meta.env.DEV && !import.meta.env.VITE_INSTANCE_URL;

export const createWidget = (widget: WidgetConfig, dashboardId: string): React.ReactElement | null => {
  // Use placeholder widgets in development mode without Sisense
  if (isDevelopmentMode) {
    return (
      <PlaceholderWidget
        key={widget.id}
        title={widget.title}
        position={widget.position}
        type={widget.type as 'chart' | 'kpi' | 'table' | 'filter' | 'header' | 'info' | 'text'}
        widgetId={widget.id}
        config={widget.config}
      />
    );
  }

  // Use WidgetById to load the actual Sisense widget
  return (
    <ErrorBoundary key={widget.id}>
      <SisenseWidget
        widgetId={widget.id}
        dashboardId={dashboardId}
        title={widget.title}
        position={widget.position}
      />
    </ErrorBoundary>
  );
};