import React from 'react';
import { SisenseWidget } from '../components/widgets/SisenseWidget/SisenseWidget';
import { PlaceholderWidget } from '../components/widgets/PlaceholderWidget/PlaceholderWidget';
import { ErrorBoundary } from '../components/ErrorBoundary/ErrorBoundary';
import type { WidgetConfig } from '../types/dashboard.types';

// Check if we're in development mode without Sisense connection
// When VITE_INSTANCE_URL is set, we'll use real Sisense widgets with the official WidgetById component
// When not set (development), we'll use placeholder widgets for development purposes
const isDevelopmentMode = import.meta.env.DEV && !import.meta.env.VITE_INSTANCE_URL;

/**
 * Creates a widget component using the official Sisense WidgetById component
 * 
 * This factory function creates widgets that use the official Sisense SDK WidgetById component
 * as documented at: https://sisense.dev/guides/sdk/modules/sdk-ui/fusion-assets/function.WidgetById.html
 * 
 * @param widget - Widget configuration containing ID, position, and other properties
 * @param dashboardId - The Sisense dashboard ID that contains the widget
 * @returns React element with either a Sisense widget or placeholder widget
 */
export const createWidget = (widget: WidgetConfig, dashboardId: string): React.ReactElement | null => {
  // Use placeholder widgets in development mode without Sisense connection
  if (isDevelopmentMode) {
    return (
      <PlaceholderWidget
        key={widget.id}
        title={widget.title}
        position={widget.position}
        type={widget.type as 'chart' | 'kpi' | 'table' | 'filter' | 'header' | 'info' | 'text'}
        widgetId={widget.id}
        config={widget.config}
      />
    );
  }

  // Use the official Sisense WidgetById component to load actual Sisense widgets
  // This component is a thin wrapper on the ChartWidget component and renders widgets
  // created in a Sisense Fusion instance
  return (
    <ErrorBoundary key={widget.id}>
      <SisenseWidget
        widgetId={widget.id}
        dashboardId={dashboardId}
        title={widget.title}
        position={widget.position}
        includeDashboardFilters={true}
        styleOptions={{
          height: widget.position.h * 120, // Dynamic height based on grid position
          // Width is handled by CSS layout, so we don't need to specify it here
          // The WidgetById component will use 100% width by default within its container
        }}
      />
    </ErrorBoundary>
  );
};